# N8N 企业版中文版 - Hugging Face 部署配置示例
# 复制此文件为 .env 并填入实际值

# ===========================================
# 数据库配置（强烈推荐使用外部 PostgreSQL）
# ===========================================
DB_POSTGRESDB_HOST=your-postgresql-host.com
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=your-username
DB_POSTGRESDB_PASSWORD=your-password

# ===========================================
# N8N 基础配置
# ===========================================
# 加密密钥（必须设置，用于加密敏感数据）
N8N_ENCRYPTION_KEY=your-very-secure-encryption-key-here

# Webhook URL（通常自动检测，也可手动设置）
# WEBHOOK_URL=https://your-space-name.hf.space/

# ===========================================
# 企业版功能配置（开发测试用）
# ===========================================
N8N_ENTERPRISE_MOCK=true
N8N_DEFAULT_LOCALE=zh-CN
NODE_ENV=development

# 企业功能开关
N8N_SAML_ENABLED=true
N8N_LDAP_ENABLED=true
N8N_LOG_STREAMING_ENABLED=true
N8N_VARIABLES_ENABLED=true
N8N_SOURCE_CONTROL_ENABLED=true
N8N_EXTERNAL_SECRETS_ENABLED=true
N8N_WORKFLOW_HISTORY_ENABLED=true
N8N_AI_ASSISTANT_ENABLED=true

# ===========================================
# 可选配置
# ===========================================
# 时区设置
GENERIC_TIMEZONE=Asia/Shanghai

# 日志级别
N8N_LOG_LEVEL=info

# 禁用遥测
N8N_DIAGNOSTICS_ENABLED=false
N8N_VERSION_NOTIFICATIONS_ENABLED=false

# 许可证相关（企业版模拟）
N8N_LICENSE_AUTO_RENEW_ENABLED=false

# ===========================================
# 高级配置（可选）
# ===========================================
# 最大负载大小（MB）
N8N_PAYLOAD_SIZE_MAX=256

# 启用指标收集
N8N_METRICS=true

# 队列健康检查
QUEUE_HEALTH_CHECK_ACTIVE=true

# 安全配置
N8N_SECURE_COOKIE=false
N8N_EDITOR_BASE_URL=""

# 用户管理
N8N_USER_MANAGEMENT_DISABLED=false
