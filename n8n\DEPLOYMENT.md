# N8N 企业版中文版 - Hugging Face 部署指南

## 📋 部署前准备

### 1. 准备 PostgreSQL 数据库

**推荐使用外部 PostgreSQL 数据库服务：**

- [Supabase](https://supabase.com/) - 免费额度，易于使用
- [Neon](https://neon.tech/) - 现代化 PostgreSQL 服务
- [Railway](https://railway.app/) - 简单的数据库托管
- [Aiven](https://aiven.io/) - 企业级数据库服务

**创建数据库：**
```sql
CREATE DATABASE n8n;
CREATE USER n8n_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE n8n TO n8n_user;
```

### 2. 获取数据库连接信息

记录以下信息：
- 主机地址：`your-db-host.com`
- 端口：`5432`
- 数据库名：`n8n`
- 用户名：`n8n_user`
- 密码：`your_secure_password`

## 🚀 Hugging Face 部署步骤

### 步骤 1：创建新的 Space

1. 访问 [Hugging Face Spaces](https://huggingface.co/spaces)
2. 点击 "Create new Space"
3. 填写信息：
   - **Space name**: `n8n-enterprise-chs`（或您喜欢的名称）
   - **License**: `fair-code`
   - **SDK**: `Docker`
   - **Hardware**: 推荐 `CPU basic` 或更高

### 步骤 2：上传文件

将以下文件上传到您的 Space：

```
your-space/
├── Dockerfile
├── README.md
├── run.sh
├── .env.example
└── DEPLOYMENT.md
```

### 步骤 3：配置环境变量

在 Hugging Face Space 的 Settings 中添加以下环境变量：

#### 必需的环境变量：

```bash
# 数据库配置
DB_POSTGRESDB_HOST=your-postgresql-host.com
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=your-username
DB_POSTGRESDB_PASSWORD=your-password

# 加密密钥（重要！）
N8N_ENCRYPTION_KEY=your-very-secure-encryption-key-here-at-least-32-chars
```

#### 可选的环境变量：

```bash
# Webhook URL（通常自动检测）
WEBHOOK_URL=https://your-space-name.hf.space/

# 时区
GENERIC_TIMEZONE=Asia/Shanghai

# 日志级别
N8N_LOG_LEVEL=info
```

### 步骤 4：启动部署

1. 保存环境变量配置
2. Space 将自动开始构建和部署
3. 等待构建完成（通常需要 3-5 分钟）

### 步骤 5：首次设置

1. 访问您的 Space URL：`https://your-space-name.hf.space`
2. 创建管理员账户
3. 开始使用 N8N！

## 🔧 配置说明

### 数据库配置

- **SQLite**：如果不配置外部数据库，将使用 SQLite（数据可能丢失）
- **PostgreSQL**：推荐用于生产环境，数据持久化

### 企业版功能

本部署启用了以下企业版功能（仅用于开发测试）：

- ✅ 中文界面
- ✅ SAML/LDAP 认证
- ✅ 高级权限管理
- ✅ 工作流共享
- ✅ 版本控制
- ✅ AI 助手
- ✅ 变量管理
- ✅ 外部密钥

### 安全建议

1. **设置强加密密钥**：`N8N_ENCRYPTION_KEY` 至少 32 个字符
2. **使用外部数据库**：避免数据丢失
3. **定期备份**：备份工作流和配置
4. **访问控制**：设置适当的用户权限

## 🛠️ 故障排除

### 常见问题

**1. 数据库连接失败**
```bash
# 检查数据库配置
echo $DB_POSTGRESDB_HOST
echo $DB_POSTGRESDB_PORT
```

**2. 启动超时**
- 检查 Hugging Face Space 日志
- 确认数据库服务可访问
- 检查环境变量配置

**3. 中文界面未显示**
- 确认 `N8N_DEFAULT_LOCALE=zh-CN`
- 清除浏览器缓存

### 查看日志

在 Hugging Face Space 的 "Logs" 标签页查看详细日志。

### 重新部署

如需重新部署：
1. 修改任意文件（如添加空行到 README.md）
2. 提交更改
3. Space 将自动重新构建

## 📚 进阶配置

### 自定义域名

在 Hugging Face Space Settings 中可以配置自定义域名。

### 扩展功能

- 安装自定义节点
- 配置外部服务集成
- 设置定时任务

### 备份策略

1. **工作流备份**：定期导出工作流 JSON
2. **数据库备份**：使用数据库服务的备份功能
3. **配置备份**：保存环境变量配置

## ⚠️ 重要提醒

- 本部署仅用于开发测试
- 企业版功能为模拟版本
- 生产环境请购买官方许可证
- 定期更新以获取最新功能

## 🤝 获取帮助

- [N8N 官方文档](https://docs.n8n.io)
- [Hugging Face Spaces 文档](https://huggingface.co/docs/hub/spaces)
- [GitHub Issues](https://github.com/deluxebear/n8n/issues)

---

祝您使用愉快！🎉
