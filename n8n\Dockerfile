# 使用企业版 n8n 中文版作为基础镜像
FROM ghcr.io/deluxebear/n8n:chs

# 添加维护者信息
LABEL maintainer="Hugging Face Deployment"

# 切换到 root 用户进行系统配置
USER root

# 企业版特有环境变量 - 适配 Hugging Face 部署
ENV N8N_HOST=0.0.0.0 \
    N8N_PORT=7860 \
    N8N_PROTOCOL=https \
    # 企业版核心配置
    N8N_ENTERPRISE_MOCK=true \
    N8N_DEFAULT_LOCALE=zh-CN \
    NODE_ENV=development \
    # 企业功能启用
    N8N_SAML_ENABLED=true \
    N8N_LDAP_ENABLED=true \
    N8N_LOG_STREAMING_ENABLED=true \
    N8N_VARIABLES_ENABLED=true \
    N8N_SOURCE_CONTROL_ENABLED=true \
    N8N_EXTERNAL_SECRETS_ENABLED=true \
    N8N_WORKFLOW_HISTORY_ENABLED=true \
    N8N_AI_ASSISTANT_ENABLED=true \
    # 基础配置
    GENERIC_TIMEZONE=Asia/Shanghai \
    N8N_METRICS=true \
    QUEUE_HEALTH_CHECK_ACTIVE=true \
    N8N_PAYLOAD_SIZE_MAX=256 \
    # 数据库配置 - 使用环境变量
    DB_TYPE=postgresdb \
    # 添加超时配置
    WAIT_TIMEOUT=30 \
    # 添加日志级别
    N8N_LOG_LEVEL=info \
    # 许可证相关（企业版模拟）
    N8N_LICENSE_AUTO_RENEW_ENABLED=false \
    N8N_DIAGNOSTICS_ENABLED=false \
    N8N_VERSION_NOTIFICATIONS_ENABLED=false \
    # 修复 N8N_RELEASE_TYPE 问题
    N8N_RELEASE_TYPE=stable \
    # Hugging Face 特定配置
    N8N_SECURE_COOKIE=false \
    N8N_EDITOR_BASE_URL="" \
    # 禁用用户管理（简化部署）
    N8N_USER_MANAGEMENT_DISABLED=false \
    # 设置默认加密密钥（生产环境请使用环境变量）
    N8N_ENCRYPTION_KEY="n8n-default-encryption-key-change-me"

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    netcat-openbsd \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录和必要的目录
WORKDIR /home/<USER>
RUN mkdir -p /home/<USER>/.n8n && \
    chown -R node:node /home/<USER>/.n8n

# 创建启动脚本
COPY --chown=node:node run.sh /home/<USER>/run.sh
RUN chmod +x /home/<USER>/run.sh

# 暴露端口
EXPOSE 7860

# 切换到非 root 用户
USER node

# 设置数据卷
VOLUME ["/home/<USER>/.n8n"]

# 使用启动脚本
CMD ["/bin/bash", "/home/<USER>/run.sh"]
