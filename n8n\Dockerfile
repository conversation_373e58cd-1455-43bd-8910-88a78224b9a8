# N8N 企业版中文版 - 直接启动版本
# 完全不使用外部脚本，所有配置在 Dockerfile 中完成

FROM ghcr.io/deluxebear/n8n:chs

# 添加维护者信息
LABEL maintainer="Hugging Face Deployment"

# 切换到 root 用户进行配置
USER root

# 企业版环境变量配置
ENV N8N_HOST=0.0.0.0 \
    N8N_PORT=7860 \
    N8N_PROTOCOL=https \
    N8N_ENTERPRISE_MOCK=true \
    N8N_DEFAULT_LOCALE=zh-CN \
    NODE_ENV=development \
    N8N_SAML_ENABLED=true \
    N8N_LDAP_ENABLED=true \
    N8N_LOG_STREAMING_ENABLED=true \
    N8N_VARIABLES_ENABLED=true \
    N8N_SOURCE_CONTROL_ENABLED=true \
    N8N_EXTERNAL_SECRETS_ENABLED=true \
    N8N_WORKFLOW_HISTORY_ENABLED=true \
    N8N_AI_ASSISTANT_ENABLED=true \
    GENERIC_TIMEZONE=Asia/Shanghai \
    N8N_METRICS=true \
    QUEUE_HEALTH_CHECK_ACTIVE=true \
    N8N_PAYLOAD_SIZE_MAX=256 \
    N8N_LOG_LEVEL=info \
    N8N_LICENSE_AUTO_RENEW_ENABLED=false \
    N8N_DIAGNOSTICS_ENABLED=false \
    N8N_VERSION_NOTIFICATIONS_ENABLED=false \
    N8N_RELEASE_TYPE=stable \
    N8N_SECURE_COOKIE=false \
    N8N_EDITOR_BASE_URL="" \
    N8N_USER_MANAGEMENT_DISABLED=false \
    N8N_ENCRYPTION_KEY="n8n-default-encryption-key-change-me"

# 数据库配置（通过环境变量设置）
ENV DB_TYPE=postgresdb

# 创建必要目录并设置权限
RUN mkdir -p /home/<USER>/.n8n && \
    chown -R node:node /home/<USER>/.n8n

# 创建一个简单的启动脚本直接写入镜像
RUN echo '#!/bin/sh' > /start.sh && \
    echo 'echo "🚀 N8N 企业版中文版启动中..."' >> /start.sh && \
    echo 'echo "📍 监听: $N8N_HOST:$N8N_PORT"' >> /start.sh && \
    echo 'echo "🌏 语言: $N8N_DEFAULT_LOCALE"' >> /start.sh && \
    echo 'echo "🏢 企业版: $N8N_ENTERPRISE_MOCK"' >> /start.sh && \
    echo 'if [ -n "$DB_POSTGRESDB_HOST" ]; then' >> /start.sh && \
    echo '  echo "🔗 使用 PostgreSQL: $DB_POSTGRESDB_HOST"' >> /start.sh && \
    echo '  export DB_TYPE=postgresdb' >> /start.sh && \
    echo 'else' >> /start.sh && \
    echo '  echo "⚠️ 使用 SQLite 数据库"' >> /start.sh && \
    echo '  export DB_TYPE=sqlite' >> /start.sh && \
    echo '  export DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite' >> /start.sh && \
    echo 'fi' >> /start.sh && \
    echo 'echo "🎯 启动 N8N..."' >> /start.sh && \
    echo 'exec n8n start' >> /start.sh && \
    chmod +x /start.sh

# 暴露端口
EXPOSE 7860

# 切换到非 root 用户
USER node

# 设置工作目录
WORKDIR /home/<USER>

# 设置数据卷
VOLUME ["/home/<USER>/.n8n"]

# 直接使用内置脚本启动
CMD ["/start.sh"]
