# N8N 企业版中文版 - 最简化版本
# 直接启动，无额外依赖

FROM ghcr.io/deluxebear/n8n:chs

# 添加维护者信息
LABEL maintainer="Hugging Face Deployment"

# 企业版环境变量配置
ENV N8N_HOST=0.0.0.0 \
    N8N_PORT=7860 \
    N8N_PROTOCOL=https \
    N8N_ENTERPRISE_MOCK=true \
    N8N_DEFAULT_LOCALE=zh-CN \
    NODE_ENV=development \
    N8N_SAML_ENABLED=true \
    N8N_LDAP_ENABLED=true \
    N8N_LOG_STREAMING_ENABLED=true \
    N8N_VARIABLES_ENABLED=true \
    N8N_SOURCE_CONTROL_ENABLED=true \
    N8N_EXTERNAL_SECRETS_ENABLED=true \
    N8N_WORKFLOW_HISTORY_ENABLED=true \
    N8N_AI_ASSISTANT_ENABLED=true \
    GENERIC_TIMEZONE=Asia/Shanghai \
    N8N_METRICS=true \
    QUEUE_HEALTH_CHECK_ACTIVE=true \
    N8N_PAYLOAD_SIZE_MAX=256 \
    N8N_LOG_LEVEL=info \
    N8N_LICENSE_AUTO_RENEW_ENABLED=false \
    N8N_DIAGNOSTICS_ENABLED=false \
    N8N_VERSION_NOTIFICATIONS_ENABLED=false \
    N8N_RELEASE_TYPE=stable \
    N8N_SECURE_COOKIE=false \
    N8N_EDITOR_BASE_URL="" \
    N8N_USER_MANAGEMENT_DISABLED=false \
    N8N_ENCRYPTION_KEY="n8n-default-encryption-key-change-me"

# 数据库配置（通过环境变量设置）
ENV DB_TYPE=postgresdb

# 暴露端口
EXPOSE 7860

# 设置工作目录
WORKDIR /home/<USER>

# 创建数据目录
RUN mkdir -p /home/<USER>/.n8n

# 直接启动 N8N（无脚本，无额外依赖）
CMD ["n8n", "start"]
