# N8N 企业版中文版 - 简化版 Dockerfile
# 如果主 Dockerfile 有问题，可以使用这个版本

FROM ghcr.io/deluxebear/n8n:chs

# 添加维护者信息
LABEL maintainer="Hugging Face Deployment"

# 切换到 root 用户进行配置
USER root

# 安装必要工具
RUN apt-get update && apt-get install -y \
    netcat-openbsd \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 企业版环境变量配置
ENV N8N_HOST=0.0.0.0 \
    N8N_PORT=7860 \
    N8N_PROTOCOL=https \
    N8N_ENTERPRISE_MOCK=true \
    N8N_DEFAULT_LOCALE=zh-CN \
    NODE_ENV=development \
    N8N_SAML_ENABLED=true \
    N8N_LDAP_ENABLED=true \
    N8N_LOG_STREAMING_ENABLED=true \
    N8N_VARIABLES_ENABLED=true \
    N8N_SOURCE_CONTROL_ENABLED=true \
    N8N_EXTERNAL_SECRETS_ENABLED=true \
    N8N_WORKFLOW_HISTORY_ENABLED=true \
    N8N_AI_ASSISTANT_ENABLED=true \
    GENERIC_TIMEZONE=Asia/Shanghai \
    N8N_METRICS=true \
    QUEUE_HEALTH_CHECK_ACTIVE=true \
    N8N_PAYLOAD_SIZE_MAX=256 \
    DB_TYPE=postgresdb \
    WAIT_TIMEOUT=30 \
    N8N_LOG_LEVEL=info \
    N8N_LICENSE_AUTO_RENEW_ENABLED=false \
    N8N_DIAGNOSTICS_ENABLED=false \
    N8N_VERSION_NOTIFICATIONS_ENABLED=false \
    N8N_RELEASE_TYPE=stable \
    N8N_SECURE_COOKIE=false \
    N8N_EDITOR_BASE_URL="" \
    N8N_USER_MANAGEMENT_DISABLED=false \
    N8N_ENCRYPTION_KEY="n8n-default-encryption-key-change-me"

# 创建必要目录
WORKDIR /home/<USER>
RUN mkdir -p /home/<USER>/.n8n && \
    chown -R node:node /home/<USER>/.n8n

# 暴露端口
EXPOSE 7860

# 切换到非 root 用户
USER node

# 设置数据卷
VOLUME ["/home/<USER>/.n8n"]

# 直接启动 N8N（不使用脚本）
CMD ["n8n", "start"]
