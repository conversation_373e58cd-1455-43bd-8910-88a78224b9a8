# 🚨 紧急部署指南

## 问题现状

基础镜像 `ghcr.io/deluxebear/n8n:chs` 存在 shell 兼容性问题：
- `/bin/bash` not found
- `/bin/sh` not found

## 🎯 立即可用的解决方案

### 方案1：使用 Dockerfile.minimal（推荐）

```dockerfile
# 最简化版本，零依赖
FROM ghcr.io/deluxebear/n8n:chs

LABEL maintainer="Hugging Face Deployment"

ENV N8N_HOST=0.0.0.0 \
    N8N_PORT=7860 \
    N8N_PROTOCOL=https \
    N8N_ENTERPRISE_MOCK=true \
    N8N_DEFAULT_LOCALE=zh-CN \
    NODE_ENV=development \
    [... 其他环境变量 ...]

EXPOSE 7860
WORKDIR /home/<USER>
RUN mkdir -p /home/<USER>/.n8n

CMD ["n8n", "start"]
```

### 方案2：使用 Dockerfile.direct

- 在构建时创建启动脚本
- 避免外部文件依赖
- 包含数据库检测逻辑

## 🚀 立即部署步骤

### 选择方案1（最简单）：

1. **重命名文件**：
   ```
   Dockerfile.minimal → Dockerfile
   ```

2. **上传到 Hugging Face Space**

3. **配置环境变量**：
   ```
   DB_POSTGRESDB_HOST=your-db-host
   DB_POSTGRESDB_PASSWORD=your-password
   N8N_ENCRYPTION_KEY=your-32-char-key
   ```

4. **等待构建完成**

### 选择方案2（功能更全）：

1. **重命名文件**：
   ```
   Dockerfile.direct → Dockerfile
   ```

2. **其他步骤同方案1**

## ✅ 预期结果

- ✅ 构建成功
- ✅ 容器正常启动
- ✅ N8N 中文界面
- ✅ 企业版功能可用
- ✅ 数据库连接正常

## 🔧 环境变量配置

### 必需变量：
```
DB_POSTGRESDB_HOST=your-postgresql-host.com
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=your-username
DB_POSTGRESDB_PASSWORD=your-password
N8N_ENCRYPTION_KEY=your-very-secure-32-character-key
```

### 可选变量：
```
WEBHOOK_URL=https://your-space.hf.space/
GENERIC_TIMEZONE=Asia/Shanghai
N8N_LOG_LEVEL=info
```

## 🛠️ 故障排除

### 如果仍然失败：

1. **检查基础镜像**：
   - 确认 `ghcr.io/deluxebear/n8n:chs` 可访问
   - 查看镜像是否更新

2. **简化到极致**：
   ```dockerfile
   FROM ghcr.io/deluxebear/n8n:chs
   ENV N8N_HOST=0.0.0.0
   ENV N8N_PORT=7860
   EXPOSE 7860
   CMD ["n8n", "start"]
   ```

3. **联系支持**：
   - 查看 Hugging Face 构建日志
   - 检查 deluxebear/n8n 项目状态

## 📋 文件优先级

1. **Dockerfile.minimal** - 最高兼容性
2. **Dockerfile.direct** - 功能完整
3. **Dockerfile.simple** - 中等复杂度
4. **Dockerfile** - 标准版（可能有兼容性问题）

## ⚡ 快速测试

本地测试命令：
```bash
# 构建测试
docker build -f Dockerfile.minimal -t n8n-test .

# 运行测试
docker run -p 5678:7860 -e N8N_ENCRYPTION_KEY=test-key n8n-test

# 访问测试
curl http://localhost:5678
```

---

**紧急联系**：如果所有方案都失败，可能需要等待基础镜像修复或使用其他 N8N 镜像。
