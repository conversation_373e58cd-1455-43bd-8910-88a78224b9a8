# 🚀 N8N 企业版中文版 - Hugging Face 一键部署指南

基于 `deluxebear/n8n:chs` 的完整 Hugging Face 部署方案，包含所有企业版功能。

## 📦 部署包内容

```
n8n/
├── Dockerfile              # 优化的 Hugging Face 部署镜像
├── README.md               # 项目说明（符合 HF 规范）
├── run.sh                  # 简化的启动脚本
├── .env.example            # 环境变量配置模板
├── .dockerignore           # Docker 构建优化
├── docker-compose.yml      # 本地测试环境
├── start.sh                # 本地快速启动脚本
├── DEPLOYMENT.md           # 详细部署指南
└── HUGGINGFACE_DEPLOY.md   # 本文档
```

## 🎯 一键部署步骤

### 1. 创建 Hugging Face Space

1. 访问 [Hugging Face Spaces](https://huggingface.co/spaces)
2. 点击 "Create new Space"
3. 配置：
   - **Name**: `n8n-enterprise-chs`
   - **SDK**: `Docker`
   - **Hardware**: `CPU basic` 或更高
   - **Visibility**: `Public` 或 `Private`

### 2. 上传部署文件

将 `n8n/` 目录下的所有文件上传到您的 Space：

**必需文件：**
- `Dockerfile` ✅
- `README.md` ✅
- `run.sh` ✅

**可选文件：**
- `.env.example` - 配置参考
- `DEPLOYMENT.md` - 详细说明
- `.dockerignore` - 构建优化

### 3. 配置环境变量

在 Space Settings → Variables 中添加：

#### 🔑 必需变量

```bash
# 数据库配置（推荐使用 Supabase 或 Neon）
DB_POSTGRESDB_HOST=your-db-host.com
DB_POSTGRESDB_PORT=5432
DB_POSTGRESDB_DATABASE=n8n
DB_POSTGRESDB_USER=your-username
DB_POSTGRESDB_PASSWORD=your-password

# 加密密钥（重要！至少32字符）
N8N_ENCRYPTION_KEY=your-very-secure-encryption-key-here-32chars
```

#### 🔧 可选变量

```bash
# Webhook URL（通常自动检测）
WEBHOOK_URL=https://your-space-name.hf.space/

# 时区设置
GENERIC_TIMEZONE=Asia/Shanghai

# 日志级别
N8N_LOG_LEVEL=info
```

### 4. 启动部署

保存配置后，Space 将自动构建和部署（约 3-5 分钟）。

## ✨ 功能特性

### 🌏 中文界面
- 完整的中文翻译
- 本地化的用户体验
- 中文文档和帮助

### 🏢 企业版功能（测试用）
- ✅ SAML/LDAP 单点登录
- ✅ 高级权限管理
- ✅ 工作流共享和协作
- ✅ Git 版本控制
- ✅ 环境变量管理
- ✅ 外部密钥集成
- ✅ 工作流历史记录
- ✅ AI 助手和问答
- ✅ 洞察仪表板

### 🔗 400+ 集成
- 主流云服务（AWS、Azure、GCP）
- 数据库（MySQL、PostgreSQL、MongoDB）
- API 服务（REST、GraphQL、Webhook）
- 通信工具（Slack、Teams、Discord）
- 营销工具（Mailchimp、HubSpot）
- 更多...

## 🛠️ 本地测试

### 使用 Docker Compose

```bash
# 克隆或下载文件到本地
cd n8n/

# 启动服务（自动创建数据库）
docker-compose up -d

# 访问应用
open http://localhost:5678
```

### 使用快速启动脚本

```bash
# Linux/macOS
chmod +x start.sh
./start.sh

# Windows
# 直接运行 docker-compose up -d
```

## 📊 推荐的数据库服务

### 1. Supabase（推荐）
- 免费额度：500MB 存储
- 易于设置和管理
- 内置备份功能

### 2. Neon
- 现代化 PostgreSQL
- 按需扩展
- 优秀的性能

### 3. Railway
- 简单的部署流程
- 自动备份
- 合理的定价

### 4. Aiven
- 企业级可靠性
- 全球多区域
- 高级安全功能

## 🔒 安全建议

1. **强加密密钥**：使用至少 32 字符的随机字符串
2. **数据库安全**：启用 SSL 连接，使用强密码
3. **访问控制**：设置适当的用户权限
4. **定期备份**：备份工作流和数据库
5. **监控日志**：定期检查访问日志

## 🚨 故障排除

### 常见问题

**1. 构建失败 "apt-get: not found"**
- ✅ **已修复**：基础镜像使用 Alpine Linux，已改用 `apk` 包管理器
- 如仍有问题，使用简化版 `Dockerfile.simple`

**2. 启动脚本错误 "Command not found"**
- 解决方案：使用简化版 Dockerfile
- 将 `Dockerfile.simple` 重命名为 `Dockerfile`
- 重新部署

**3. 构建失败（其他原因）**
- 检查 Dockerfile 语法
- 确认基础镜像 `ghcr.io/deluxebear/n8n:chs` 可访问
- 查看 Hugging Face 构建日志

**3. 数据库连接失败**
- 验证数据库配置和凭据
- 检查数据库服务器网络可达性
- 确认数据库服务状态
- 检查防火墙设置

**4. 启动超时或内存不足**
- 升级 Hugging Face 硬件配置
- 检查环境变量设置
- 查看应用日志

**5. 中文界面未显示**
- 确认 `N8N_DEFAULT_LOCALE=zh-CN`
- 清除浏览器缓存
- 检查企业版模拟是否启用

### 🔄 多种部署方案

#### 方案1：标准版（推荐）
- 使用 `Dockerfile` + `run.sh`
- 支持数据库自动检测
- 完整的启动日志

#### 方案2：简化版
- 使用 `Dockerfile.simple`
- 直接启动，无启动脚本
- 适用于脚本兼容性问题

#### 方案3：最简版（推荐用于故障排除）
- 使用 `Dockerfile.minimal`
- 零额外依赖，直接启动
- 最高兼容性

**使用方法**：将对应文件重命名为 `Dockerfile` 并重新部署

### 调试命令

```bash
# 本地测试
docker build -t n8n-test .
docker run -p 5678:7860 n8n-test

# 查看容器日志
docker logs container-name

# 进入容器调试
docker exec -it container-name /bin/bash

# 测试数据库连接
nc -zv db-host 5432
```

### 环境变量检查清单

确保以下环境变量已正确设置：

- ✅ `DB_POSTGRESDB_HOST` - 数据库主机地址
- ✅ `DB_POSTGRESDB_PASSWORD` - 数据库密码
- ✅ `N8N_ENCRYPTION_KEY` - 加密密钥（至少32字符）
- ⚠️ `WEBHOOK_URL` - 通常自动检测
- ⚠️ `SPACE_ID` - Hugging Face 自动提供

## 📈 性能优化

### Hugging Face 硬件建议

- **开发测试**: CPU basic
- **小团队**: CPU upgrade
- **生产环境**: GPU T4 small

### 数据库优化

- 使用连接池
- 定期清理历史数据
- 优化查询索引

## 🔄 更新和维护

### 更新 N8N 版本

1. 修改 Dockerfile 中的基础镜像版本
2. 提交更改到 Space
3. 等待自动重新构建

### 备份策略

1. **工作流备份**：定期导出 JSON
2. **数据库备份**：使用数据库服务的备份功能
3. **配置备份**：保存环境变量配置

## ⚠️ 重要声明

- **仅用于开发测试**：企业版功能为模拟版本
- **生产环境**：请购买官方 N8N 企业版许可证
- **数据安全**：请妥善保管数据库凭据
- **合规使用**：遵守相关法律法规

## 🤝 支持和帮助

- [N8N 官方文档](https://docs.n8n.io)
- [deluxebear/n8n GitHub](https://github.com/deluxebear/n8n)
- [Hugging Face Spaces 文档](https://huggingface.co/docs/hub/spaces)

---

🎉 **祝您使用愉快！** 

如有问题，请参考 `DEPLOYMENT.md` 获取更详细的说明。
