---
title: N8N 企业版中文版
emoji: 🔧
colorFrom: blue
colorTo: green
sdk: docker
sdk_version: "4.36.0"
app_file: Dockerfile
pinned: false
license: other
short_description: N8N 工作流自动化平台企业版中文版 - 支持400+集成和AI功能
---

# N8N 企业版中文版 🚀

基于 [deluxebear/n8n:chs](https://github.com/deluxebear/n8n/tree/chs) 的 N8N 工作流自动化平台企业版中文版，专为 Hugging Face 平台优化部署。

## ✨ 特性

- 🌏 **完整中文界面** - 基于官方最新版本的完整中文翻译
- 🏢 **企业版功能** - 启用所有企业版功能（开发测试用）
- 🤖 **AI 原生支持** - 内置 AI 助手和 LangChain 集成
- 🔗 **400+ 集成** - 支持主流服务和 API
- 🛡️ **安全可控** - 支持 SAML、LDAP、权限管理等企业级安全功能
- 📊 **可视化编辑** - 直观的工作流设计界面

## 🚀 快速开始

1. **配置数据库**（推荐使用外部 PostgreSQL）：
   ```bash
   DB_POSTGRESDB_HOST=your-db-host
   DB_POSTGRESDB_PORT=5432
   DB_POSTGRESDB_DATABASE=n8n
   DB_POSTGRESDB_USER=your-username
   DB_POSTGRESDB_PASSWORD=your-password
   ```

2. **访问应用**：
   - 应用将在端口 7860 上运行
   - 首次访问时需要创建管理员账户

## 🏢 企业版功能

本版本启用了以下企业版功能（仅用于开发测试）：

### 🔐 认证和安全
- ✅ SAML 单点登录
- ✅ LDAP/Active Directory 集成
- ✅ 高级权限管理
- ✅ API 密钥作用域

### 👥 协作功能
- ✅ 工作流和凭证共享
- ✅ 项目管理（管理员/编辑者/查看者角色）
- ✅ 文件夹组织

### 🛠️ 开发运维
- ✅ Git 源代码控制
- ✅ 环境变量管理
- ✅ 外部密钥管理
- ✅ 工作流版本历史
- ✅ 编辑器调试功能

### 🤖 AI 功能
- ✅ AI 助手
- ✅ AI 问答功能
- ✅ 洞察仪表板

## 🔧 环境变量配置

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `DB_POSTGRESDB_HOST` | PostgreSQL 主机地址 | - |
| `DB_POSTGRESDB_PORT` | PostgreSQL 端口 | 5432 |
| `DB_POSTGRESDB_DATABASE` | 数据库名称 | n8n |
| `DB_POSTGRESDB_USER` | 数据库用户名 | n8n |
| `DB_POSTGRESDB_PASSWORD` | 数据库密码 | - |
| `WEBHOOK_URL` | Webhook 基础URL | 自动检测 |
| `N8N_ENCRYPTION_KEY` | 加密密钥 | 请设置强密钥 |

## ⚠️ 重要说明

- **企业版功能仅用于开发测试**：本部署启用了企业版功能模拟，仅供学习和测试使用
- **数据安全**：建议使用外部 PostgreSQL 数据库以确保数据持久性
- **生产使用**：如需生产环境使用，请购买官方 N8N 企业版许可证

## 📚 相关资源

- [N8N 官方文档](https://docs.n8n.io)
- [deluxebear/n8n GitHub](https://github.com/deluxebear/n8n/tree/chs)
- [N8N 集成列表](https://n8n.io/integrations)
- [工作流模板](https://n8n.io/workflows)

## 🤝 贡献

本项目基于 [deluxebear/n8n](https://github.com/deluxebear/n8n) 项目，感谢原作者的贡献。

---

**免责声明**：本中文版仅供个人测试使用，如因使用本版本引起的任何法律问题，由使用者自行承担。
