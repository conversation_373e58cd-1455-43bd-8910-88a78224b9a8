# N8N 企业版中文版 - 本地测试环境
# 使用方法：docker-compose up -d

version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: n8n-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: n8n
      POSTGRES_USER: n8n
      POSTGRES_PASSWORD: n8n_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U n8n -d n8n"]
      interval: 10s
      timeout: 5s
      retries: 5

  # N8N 企业版中文版
  n8n:
    build: .
    container_name: n8n-enterprise-chs
    restart: unless-stopped
    ports:
      - "5678:7860"
    environment:
      # 数据库配置
      DB_TYPE: postgresdb
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: 5432
      DB_POSTGRESDB_DATABASE: n8n
      DB_POSTGRESDB_USER: n8n
      DB_POSTGRESDB_PASSWORD: n8n_password
      
      # N8N 基础配置
      N8N_HOST: 0.0.0.0
      N8N_PORT: 7860
      N8N_PROTOCOL: http
      WEBHOOK_URL: http://localhost:5678/
      N8N_ENCRYPTION_KEY: n8n-local-encryption-key-change-me-in-production
      
      # 企业版功能
      N8N_ENTERPRISE_MOCK: true
      N8N_DEFAULT_LOCALE: zh-CN
      NODE_ENV: development
      
      # 企业功能开关
      N8N_SAML_ENABLED: true
      N8N_LDAP_ENABLED: true
      N8N_LOG_STREAMING_ENABLED: true
      N8N_VARIABLES_ENABLED: true
      N8N_SOURCE_CONTROL_ENABLED: true
      N8N_EXTERNAL_SECRETS_ENABLED: true
      N8N_WORKFLOW_HISTORY_ENABLED: true
      N8N_AI_ASSISTANT_ENABLED: true
      
      # 其他配置
      GENERIC_TIMEZONE: Asia/Shanghai
      N8N_LOG_LEVEL: info
      N8N_METRICS: true
      QUEUE_HEALTH_CHECK_ACTIVE: true
      N8N_PAYLOAD_SIZE_MAX: 256
      
      # 许可证相关
      N8N_LICENSE_AUTO_RENEW_ENABLED: false
      N8N_DIAGNOSTICS_ENABLED: false
      N8N_VERSION_NOTIFICATIONS_ENABLED: false
      
      # 安全配置
      N8N_SECURE_COOKIE: false
      N8N_USER_MANAGEMENT_DISABLED: false
    
    volumes:
      - n8n_data:/home/<USER>/.n8n
    
    depends_on:
      postgres:
        condition: service_healthy
    
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:7860/healthz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  n8n_data:
    driver: local

networks:
  default:
    name: n8n-network
