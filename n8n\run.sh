#!/bin/sh
set -e

echo "🚀 N8N 企业版中文版启动中..."

# 设置默认值
export N8N_HOST=${N8N_HOST:-0.0.0.0}
export N8N_PORT=${N8N_PORT:-7860}

# 数据库配置
if [ -n "$DB_POSTGRESDB_HOST" ]; then
    echo "🔗 使用 PostgreSQL 数据库"
    export DB_TYPE=postgresdb
    export DB_POSTGRESDB_HOST=${DB_POSTGRESDB_HOST}
    export DB_POSTGRESDB_PORT=${DB_POSTGRESDB_PORT:-5432}
    export DB_POSTGRESDB_DATABASE=${DB_POSTGRESDB_DATABASE:-n8n}
    export DB_POSTGRESDB_USER=${DB_POSTGRESDB_USER:-n8n}
    export DB_POSTGRESDB_PASSWORD=${DB_POSTGRESDB_PASSWORD}

    echo "数据库: $DB_POSTGRESDB_HOST:$DB_POSTGRESDB_PORT/$DB_POSTGRESDB_DATABASE"

    # 简单的数据库连接测试
    if command -v nc >/dev/null 2>&1; then
        echo "⏳ 测试数据库连接..."
        if nc -z "$DB_POSTGRESDB_HOST" "$DB_POSTGRESDB_PORT" 2>/dev/null; then
            echo "✅ 数据库连接正常"
        else
            echo "⚠️ 数据库连接测试失败，但继续启动"
        fi
    fi
else
    echo "⚠️ 使用 SQLite 数据库"
    export DB_TYPE=sqlite
    export DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite
fi

# 设置 Webhook URL
if [ -z "$WEBHOOK_URL" ] && [ -n "$SPACE_ID" ]; then
    export WEBHOOK_URL="https://${SPACE_ID}.hf.space/"
    echo "🌐 Webhook URL: $WEBHOOK_URL"
fi

# 显示配置信息
echo "📍 监听: $N8N_HOST:$N8N_PORT"
echo "🌏 语言: ${N8N_DEFAULT_LOCALE:-zh-CN}"
echo "🏢 企业版: ${N8N_ENTERPRISE_MOCK:-true}"

# 确保数据目录存在
mkdir -p /home/<USER>/.n8n

# 启动 N8N
echo "🎯 启动 N8N..."
exec n8n start
