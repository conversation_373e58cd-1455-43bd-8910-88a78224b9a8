#!/bin/bash

# N8N 企业版中文版 - 快速启动脚本
# 用于本地开发和测试

set -e

echo "🚀 N8N 企业版中文版 - 快速启动"
echo "=================================="

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建 .env 文件（如果不存在）
if [ ! -f .env ]; then
    echo "📝 创建 .env 配置文件..."
    cp .env.example .env
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
fi

# 显示配置信息
echo ""
echo "📋 当前配置："
echo "- 访问地址: http://localhost:5678"
echo "- 数据库: PostgreSQL (自动创建)"
echo "- 语言: 中文"
echo "- 企业版功能: 已启用（测试用）"
echo ""

# 询问是否继续
read -p "是否继续启动？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "取消启动"
    exit 0
fi

echo "🔧 构建和启动服务..."

# 停止现有服务（如果有）
docker-compose down 2>/dev/null || true

# 构建和启动服务
docker-compose up -d --build

echo ""
echo "⏳ 等待服务启动..."

# 等待服务就绪
timeout=60
while [ $timeout -gt 0 ]; do
    if curl -s http://localhost:5678/healthz >/dev/null 2>&1; then
        echo "✅ N8N 服务已就绪！"
        break
    fi
    echo "等待服务启动... ($timeout 秒)"
    sleep 2
    timeout=$((timeout - 2))
done

if [ $timeout -eq 0 ]; then
    echo "❌ 服务启动超时，请检查日志："
    echo "docker-compose logs n8n"
    exit 1
fi

echo ""
echo "🎉 启动成功！"
echo "=================================="
echo "📍 访问地址: http://localhost:5678"
echo "🌏 界面语言: 中文"
echo "🏢 企业版功能: 已启用"
echo ""
echo "📚 使用说明："
echo "1. 首次访问需要创建管理员账户"
echo "2. 所有企业版功能均已启用（仅用于测试）"
echo "3. 数据将保存在 Docker 卷中"
echo ""
echo "🛠️ 管理命令："
echo "- 查看日志: docker-compose logs -f"
echo "- 停止服务: docker-compose down"
echo "- 重启服务: docker-compose restart"
echo "- 清理数据: docker-compose down -v"
echo ""
echo "🌐 正在打开浏览器..."

# 尝试打开浏览器
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:5678
elif command -v open &> /dev/null; then
    open http://localhost:5678
elif command -v start &> /dev/null; then
    start http://localhost:5678
else
    echo "请手动访问: http://localhost:5678"
fi

echo ""
echo "享受使用 N8N 企业版中文版！🎯"
